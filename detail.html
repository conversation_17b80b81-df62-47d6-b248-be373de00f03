<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/css/bootstrap.min.css">
    <!-- Swiper滑块-->
    <link rel="stylesheet" href="vendor/swiper/swiper-bundle.min.css">
</head>
  <body>
    <div class="page-holder bg-light">
      <!-- 导航栏-->
      <header class="header bg-white">
        <div class="container px-lg-3">
          <nav class="navbar navbar-expand-lg navbar-light py-3 px-lg-0"><a class="navbar-brand" href="index.html"><span class="fw-bold text-uppercase text-dark">精品店</span></a>
            <button class="navbar-toggler navbar-toggler-end" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="切换导航"><span class="navbar-toggler-icon"></span></button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
              <ul class="navbar-nav me-auto">
                <li class="nav-item">
                  <a class="nav-link active" href="index.html">首页</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="shop.html">商店</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="detail.html">产品详情</a>
                </li>
                <li class="nav-item dropdown"><a class="nav-link dropdown-toggle" id="pagesDropdown" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">页面</a>
                  <div class="dropdown-menu mt-3 shadow-sm" aria-labelledby="pagesDropdown"><a class="dropdown-item border-0 transition-link" href="index.html">首页</a><a class="dropdown-item border-0 transition-link" href="shop.html">分类</a><a class="dropdown-item border-0 transition-link" href="detail.html">产品详情</a><a class="dropdown-item border-0 transition-link" href="cart.html">购物车</a><a class="dropdown-item border-0 transition-link" href="checkout.html">结算</a></div>
                </li>
              </ul>
              <ul class="navbar-nav ms-auto">               
                <li class="nav-item"><a class="nav-link" href="cart.html"> <i class="fas fa-dolly-flatbed me-1 text-gray"></i>购物车<small class="text-gray fw-normal">(2)</small></a></li>
                <li class="nav-item"><a class="nav-link" href="sign.html"> <i class="fas fa-user me-1 text-gray fw-normal"></i>登录</a></li>
              </ul>
            </div>
          </nav>
        </div>
      </header>
      <section class="py-5">
        <div class="container">
          <div class="row mb-5">
            <div class="col-lg-6">
             <div class="product-slider-container">
  <div class="row m-sm-0">
    <div class="col-sm-2 p-sm-0 order-2 order-sm-1 mt-2 mt-sm-0 px-xl-2">
      <div class="swiper product-slider-thumbs">
        <div class="swiper-wrapper">
          <div class="swiper-slide h-auto swiper-thumb-item mb-3">
            <img class="w-100" src="img/product-detail-1.jpg" alt="Thumbnail 1">
          </div>
          <div class="swiper-slide h-auto swiper-thumb-item mb-3">
            <img class="w-100" src="img/product-detail-2.jpg" alt="Thumbnail 2">
          </div>
          <div class="swiper-slide h-auto swiper-thumb-item mb-3">
            <img class="w-100" src="img/product-detail-3.jpg" alt="Thumbnail 3">
          </div>
          <div class="swiper-slide h-auto swiper-thumb-item mb-3">
            <img class="w-100" src="img/product-detail-4.jpg" alt="Thumbnail 4">
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-10 order-1 order-sm-2">
      <div class="swiper product-slider">
        <div class="swiper-wrapper">
          <div class="swiper-slide h-auto">
            <a class="glightbox product-view" href="img/product-detail-1.jpg" data-gallery="gallery2" data-glightbox="Product 1">
              <img class="img-fluid" src="img/product-detail-1.jpg" alt="Product 1">
            </a>
          </div>
          <div class="swiper-slide h-auto">
            <a class="glightbox product-view" href="img/product-detail-2.jpg" data-gallery="gallery2" data-glightbox="Product 2">
              <img class="img-fluid" src="img/product-detail-2.jpg" alt="Product 2">
            </a>
          </div>
          <div class="swiper-slide h-auto">
            <a class="glightbox product-view" href="img/product-detail-3.jpg" data-gallery="gallery2" data-glightbox="Product 3">
              <img class="img-fluid" src="img/product-detail-3.jpg" alt="Product 3">
            </a>
          </div>
          <div class="swiper-slide h-auto">
            <a class="glightbox product-view" href="img/product-detail-4.jpg" data-gallery="gallery2" data-glightbox="Product 4">
              <img class="img-fluid" src="img/product-detail-4.jpg" alt="Product 4">
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
            </div>
            <!-- 产品详情-->
            <div class="col-lg-6">
              <ul class="list-inline mb-2 text-sm">
                <li class="list-inline-item m-0"><i class="fas fa-star small text-warning"></i></li>
                <li class="list-inline-item m-0 1"><i class="fas fa-star small text-warning"></i></li>
                <li class="list-inline-item m-0 2"><i class="fas fa-star small text-warning"></i></li>
                <li class="list-inline-item m-0 3"><i class="fas fa-star small text-warning"></i></li>
                <li class="list-inline-item m-0 4"><i class="fas fa-star small text-warning"></i></li>
              </ul>
              <h1>红色数字智能手表</h1>
              <p class="text-muted lead">¥250</p>
              <p class="text-sm mb-4">炽焰RedPulse 智能手表以一抹炽热红为主色调,搭配哑光金属边框与流线型表身,彰显个性与活力。46mm高清AMOLED触控屏,色彩鲜艳、细节清晰，即使在强光下也能清晰读数。表带采用亲肤硅胶材质，透气防汗，搭配快拆设计，轻松切换不同风格表带，满足运动、商务、休闲多场景需求。</p>
              <div class="row align-items-stretch mb-4">
                <div class="col-sm-3 pl-sm-0"><a class="btn btn-dark btn-sm btn-block h-100 d-flex align-items-center justify-content-center px-0" href="cart.html">加入购物车</a></div>
              </div>
              <ul class="list-unstyled small d-inline-block">
                <li class="px-3 py-2 mb-1 bg-white"><strong class="text-uppercase">SKU:</strong><span class="ms-2 text-muted">039</span></li>
                <li class="px-3 py-2 mb-1 bg-white text-muted"><strong class="text-uppercase text-dark">分类:</strong><a class="reset-anchor ms-2" href="#!" style="color: black;">演示产品</a></li>
                <li class="px-3 py-2 mb-1 bg-white text-muted"><strong class="text-uppercase text-dark">标签:</strong><a class="reset-anchor ms-2" href="#!" style="color: black;">创新</a></li>
              </ul>
            </div>
          </div>
          <!-- 详情选项卡-->
          <ul class="nav nav-tabs border-0" id="myTab" role="tablist">
            <li class="nav-item"><a class="nav-link text-uppercase active" id="description-tab" data-bs-toggle="tab" href="#description" role="tab" aria-controls="description" aria-selected="true">描述</a></li>
            <li class="nav-item"><a class="nav-link text-uppercase" id="reviews-tab" data-bs-toggle="tab" href="#reviews" role="tab" aria-controls="reviews" aria-selected="false">评价</a></li>
          </ul>
          <div class="tab-content mb-5" id="myTabContent">
            <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
              <div class="p-4 p-lg-5 bg-white">
                <h6 class="text-uppercase">产品描述</h6>
                <p class="text-muted text-sm mb-0">炽焰RedPulse 智能手表以一抹炽热红为主色调，搭配哑光金属边框与流线型表身,彰显个性与活力。46mm高清AMOLED触控屏,色彩鲜艳、细节清晰,即使在强光下也能清晰读数。表带采用亲肤硅胶材质,透气防汗,搭配快拆设计,轻松切换不同风格表带,满足运动、商务、休闲多场景需求。</p>
              </div>
            </div>
            <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
              <div class="p-4 p-lg-5 bg-white">
                <div class="row">
                  <div class="col-lg-8">
                    <div class="d-flex mb-3">
                      <div class="ms-3 flex-shrink-1">
                        <h6 class="mb-0 text-uppercase">李先生</h6>
                        <p class="small text-muted mb-0 text-uppercase">2025年5月20日</p>
                        <p class="text-sm mb-0 text-muted">红色真的太炸了!健身房里一眼就能认出我的表。心率监测超准,昨天带学员做HIIT,它居然能实时提醒我强度过高要调整，安全感拉满！</p>
                      </div>
                    </div>
                    <div class="d-flex">
                      <div class="ms-3 flex-shrink-1">
                        <h6 class="mb-0 text-uppercase">张女士</h6>
                        <p class="small text-muted mb-0 text-uppercase">2023年5月20日</p>
                        <p class="text-sm mb-0 text-muted">双频GPS定位太稳了!上次跑山路线复杂,它居然能精准记录海拔变化,轨迹图和官方数据误差不到1%。充电快也是刚需，跑完洗个澡的功夫就满血复活。</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 相关产品-->
          <h2 class="h5 text-uppercase mb-4">相关产品</h2>
          <div class="row">
            <!-- 产品-->
            <div class="col-lg-3 col-sm-6">
              <div class="product text-center skel-loader">
                <div class="d-block mb-3 position-relative"><a class="d-block" href="detail.html"><img class="img-fluid w-100" src="img/product-1.jpg" alt="..."></a>
                  <div class="product-overlay">
                    <ul class="mb-0 list-inline">
                      <li class="list-inline-item m-0 p-0"><a class="btn btn-sm btn-dark" href="cart.html">加入购物车</a></li>
                    </ul>
                  </div>
                </div>
                <h6> <a class="reset-anchor" href="detail.html">Kui Ye Chen的AirPods</a></h6>
                <p class="small text-muted">¥250</p>
              </div>
            </div>
            <!-- 产品-->
            <div class="col-lg-3 col-sm-6">
              <div class="product text-center skel-loader">
                <div class="d-block mb-3 position-relative"><a class="d-block" href="detail.html"><img class="img-fluid w-100" src="img/product-2.jpg" alt="..."></a>
                  <div class="product-overlay">
                    <ul class="mb-0 list-inline">
                      <li class="list-inline-item m-0 p-0"><a class="btn btn-sm btn-dark" href="cart.html">加入购物车</a></li>
                    </ul>
                  </div>
                </div>
                <h6> <a class="reset-anchor" href="detail.html">Air Jordan 12 体育馆红</a></h6>
                <p class="small text-muted">¥300</p>
              </div>
            </div>
            <!-- 产品-->
            <div class="col-lg-3 col-sm-6">
              <div class="product text-center skel-loader">
                <div class="d-block mb-3 position-relative"><a class="d-block" href="detail.html"><img class="img-fluid w-100" src="img/product-3.jpg" alt="..."></a>
                  <div class="product-overlay">
                    <ul class="mb-0 list-inline">
                      <li class="list-inline-item m-0 p-0"><a class="btn btn-sm btn-dark" href="cart.html">加入购物车</a></li>
                    </ul>
                  </div>
                </div>
                <h6> <a class="reset-anchor" href="detail.html">青色棉质T恤</a></h6>
                <p class="small text-muted">¥25</p>
              </div>
            </div>
            <!-- 产品-->
            <div class="col-lg-3 col-sm-6">
              <div class="product text-center skel-loader">
                <div class="d-block mb-3 position-relative"><a class="d-block" href="detail.html"><img class="img-fluid w-100" src="img/product-4.jpg" alt="..."></a>
                  <div class="product-overlay">
                    <ul class="mb-0 list-inline">
                      <li class="list-inline-item m-0 p-0"><a class="btn btn-sm btn-dark" href="cart.html">加入购物车</a></li>
                    </ul>
                  </div>
                </div>
                <h6> <a class="reset-anchor" href="detail.html">Timex 男女通用原厂款</a></h6>
                <p class="small text-muted">¥351</p>
              </div>
            </div>
          </div>
        </div>
      </section>
<footer class="bg-dark text-white" style="background-color: #000 !important; ">
    <div class="container py-4">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="small text-muted mb-0" style="color: #fff !important;">&copy; 保留所有权利归:</p>
            </div>
        </div>
    </div>
</footer>
      <!-- JavaScript文件-->
      <script src="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/js/bootstrap.bundle.min.js"></script>
      <script src="vendor/glightbox/js/glightbox.min.js"></script>
      <script src="vendor/nouislider/nouislider.min.js"></script>
      <script src="vendor/swiper/swiper-bundle.min.js"></script>
      <script src="vendor/choices.js/public/assets/scripts/choices.min.js"></script>
      <script src="js/front.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 初始化灯箱
  const lightbox = GLightbox({
    selector: '.glightbox'
  });
  // 纯净的主滑块配置
  const mainSwiper = new Swiper('.product-slider', {
    loop: false,
    spaceBetween: 10
  });
  // 缩略图滑块配置保持不变
  const thumbsSwiper = new Swiper('.product-slider-thumbs', {
    loop: false,
    spaceBetween: 10,
    slidesPerView: 4,
    freeMode: true,
    watchSlidesProgress: true,
    direction: 'vertical',
    breakpoints: {
      768: {
        direction: 'vertical',
        slidesPerView: 4,
      },
      0: {
        direction: 'horizontal',
        slidesPerView: 4,
      }
    }
  });
  // 滑块联动
  mainSwiper.controller.control = thumbsSwiper;
  thumbsSwiper.controller.control = mainSwiper;
  // 缩略图点击事件
  document.querySelectorAll('.swiper-thumb-item').forEach((thumb, index) => {
    thumb.addEventListener('click', () => {
      mainSwiper.slideTo(index);
    });
  });
});
</script>
    </div>
  </body>
</html>
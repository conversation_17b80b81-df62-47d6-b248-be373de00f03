<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/css/bootstrap.min.css">
        <style>
      .login-container {
        max-width: 400px;
        margin: 9.11rem auto;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      }
      .login-header {
        text-align: center;
        margin-bottom: 2rem;
      }
      .login-header h2 {
        color: #333;
      }
      .form-control.is-invalid {
        border-color: #dc3545;
      }
      .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
      }
      .register-link {
        text-align: center;
        margin-top: 1.5rem;
      }
    </style>
</head>
  <body>
    <div class="page-holder">
      <!-- 导航栏-->
      <header class="header bg-white">
        <div class="container px-lg-3">
          <nav class="navbar navbar-expand-lg navbar-light py-3 px-lg-0"><a class="navbar-brand" href="index.html"><span class="fw-bold text-uppercase text-dark">精品店</span></a>
            <button class="navbar-toggler navbar-toggler-end" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="切换导航"><span class="navbar-toggler-icon"></span></button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
              <ul class="navbar-nav me-auto">
                <li class="nav-item">
                  <a class="nav-link" href="index.html">首页</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="shop.html">商店</a>
                </li>
                <li class="nav-item">
                 <a class="nav-link" href="detail.html">产品详情</a>
                </li>
                <li class="nav-item dropdown"><a class="nav-link dropdown-toggle" id="pagesDropdown" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">页面</a>
                  <div class="dropdown-menu mt-3 shadow-sm" aria-labelledby="pagesDropdown"><a class="dropdown-item border-0 transition-link" href="index.html">首页</a><a class="dropdown-item border-0 transition-link" href="shop.html">分类</a><a class="dropdown-item border-0 transition-link" href="detail.html">产品详情</a><a class="dropdown-item border-0 transition-link" href="cart.html">购物车</a><a class="dropdown-item border-0 transition-link" href="checkout.html">结算</a></div>
                </li>
              </ul>
              <ul class="navbar-nav ms-auto">               
                <li class="nav-item"><a class="nav-link" href="cart.html"> <i class="fas fa-dolly-flatbed me-1 text-gray"></i>购物车<small class="text-gray fw-normal">(2)</small></a></li>
                <li class="nav-item"><a class="nav-link" href="sign.html"> <i class="fas fa-user me-1 text-gray fw-normal"></i>登录</a></li>
              </ul>
            </div>
          </nav>
        </div>
      </header>
          <div class="container">
            <div class="login-container bg-white">
              <div class="login-header">
                <h2>欢迎登录</h2>
                <p>请输入您的用户名和密码</p>
              </div>
              <form id="loginForm" novalidate>
                <div class="mb-3">
                  <label class="form-label" for="username">用户名</label>
                  <input type="text" class="form-control" id="username" required>
                  <div class="invalid-feedback">
                    请输入有效的用户名
                  </div>
                </div>
                <div class="mb-3">
                  <label class="form-label" for="password">密码</label>
                  <input type="password" class="form-control" id="password" required minlength="6">
                  <div class="invalid-feedback">
                    密码必须至少6个字符
                  </div>
                </div>
                <div class="mb-3 form-check">
                  <input type="checkbox" class="form-check-input" id="rememberMe">
                  <label class="form-check-label" for="rememberMe">记住我</label>
                </div>
                <button type="submit" class="btn btn-primary w-100">登录</button>
                <div class="register-link">
                  <p>还没有账号？<a href="reg.html">立即注册</a></p>
                </div>
                <div class="mt-3 text-center">
                  <a href="#!">忘记密码?</a>
                </div>
              </form>
            </div>
          </div>
<footer class="bg-dark text-white" style="background-color: #000 !important; ">
    <div class="container py-4">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="small text-muted mb-0" style="color: #fff !important;">&copy; 保留所有权利归:</p>
            </div>
        </div>
    </div>
</footer>
      <script src="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/js/bootstrap.bundle.min.js"></script>
      <script src="vendor/glightbox/js/glightbox.min.js"></script>
      <script src="vendor/nouislider/nouislider.min.js"></script>
      <script src="vendor/swiper/swiper-bundle.min.js"></script>
      <script src="vendor/choices.js/public/assets/scripts/choices.min.js"></script>
      <script src="js/front.js"></script>
      <script>
        // 表单验证
        (function () {
          'use strict';         
          // 获取表单元素
          const form = document.querySelector('#loginForm');
          
          form.addEventListener('submit', function (event) {
            // 阻止默认提交行为
            event.preventDefault();
            
            // 获取表单字段
            const username = document.getElementById('username');
            const password = document.getElementById('password');
            
            // 重置验证状态
            username.classList.remove('is-invalid');
            password.classList.remove('is-invalid');
            
            let isValid = true;
            
            // 验证用户名（假设用户名不能为空）
            if (username.value.trim() === '') {
              username.classList.add('is-invalid');
              isValid = false;
            }
            
            // 验证密码
            if (password.value.length < 6) {
              password.classList.add('is-invalid');
              isValid = false;
            }
            
            // 如果验证通过，跳转到index.html
            if (isValid) {
              // 这里可以添加实际的登录逻辑（如AJAX请求）
              // 模拟登录成功后跳转
              window.location.href = 'index.html';
            } else {
              // 添加was-validated类以显示验证消息
              form.classList.add('was-validated');
            }
          });
        })();
      </script>
    </div>
  </body>
</html>
/* 基础样式 */
:root {
  --primary-color: #3a3a3a;
  --secondary-color: #f8f9fa;
  --accent-color: #ff6b6b;
  --text-color: #333;
  --light-text: #999;
  --white: #fff;
  --black: #000;
  --border-radius: 4px;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}
.page-holder {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.reset-anchor{
 text-decoration: none;
}

/* 导航栏样式 */
.header {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.navbar {
  padding: 0.75rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--primary-color);
  padding: 0.5rem 0.75rem !important;
  margin: 0 0.25rem;
  transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--accent-color) !important;
}

.dropdown-menu {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: none;
}

.dropdown-item {
  padding: 0.5rem 1.5rem;
  transition: var(--transition);
}

.dropdown-item:hover {
  background-color: var(--secondary-color);
  color: var(--accent-color);
}

/* 轮播图样式 */
.course-banner {
    margin-top: 15px;
}
.course-banner .recommend {
    background-color: #fff;
    box-shadow: 1px 1px 2px 0px rgba(211, 211, 211, 0.5);
}
@media (max-width: 767px) {
    .course-banner {
        margin-top: 86px;
    }
    .carousel-indicators {
        bottom: -10px;
    }
}


/* 分类区域样式 */
.category-item {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  transition: var(--transition);
}
.category-item img {
  transition: var(--transition);
  transform: scale(1);
}
.category-item:hover img {
  transform: scale(1.05);
}
.category-item-title {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: #fff;
  text-transform: uppercase;
  letter-spacing: 0.07em;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.07);
  font-size: 0.8rem;
}
.category-item-title{
  color: black;
}
/* 产品卡片样式 */
.product {
  margin-bottom: 2rem;
  transition: var(--transition);
}
.product:hover {
  transform: translateY(-5px);
}
.product img {
  border-radius: var(--border-radius);
  transition: var(--transition);
}
.product:hover img {
  box-shadow: var(--box-shadow);
}
.product h6 {
  font-size: 1rem;
  margin-top: 1rem;
  font-weight: 500;
}
.product h6 a {
  color: var(--text-color);
  transition: var(--transition);
}
.product h6 a:hover {
  color: var(--accent-color);
}
.product p {
  font-size: 1rem;
  color: var(--accent-color);
  font-weight: 600;
}
.badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  padding: 0.35rem 0.65rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius);
}
.bg-primary {
  background-color: var(--accent-color) !important;
}
.bg-info {
  background-color: #4dabf7 !important;
}
.bg-danger {
  background-color: #ff4c4c !important;
}
.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
  border-radius: var(--border-radius);
}
.product:hover .product-overlay {
  opacity: 1;
}
/* 服务区域样式 */
.svg-icon-big {
  width: 3rem;
  height: 3rem;
}
/* 新闻订阅样式 */
.newsletter {
  background-color: var(--secondary-color);
  padding: 2rem 0;
}
.form-control {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  transition: var(--transition);
}
.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: none;
}
.btn-dark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}
.btn-dark:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}
.mb-2 a{
  color: black;
}
/* 响应式调整 */
@media (max-width: 992px) {
  .navbar-collapse {
    background-color: var(--white);
    padding: 1rem;
    margin-top: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  .category-item {
    margin-bottom: 1rem;
  }
}
@media (max-width: 768px) {
  .carousel-item img {
    height: 300px !important;
  }
  .product {
    max-width: 250px;
    margin: 0 auto 1.5rem;
  }
}
@media (max-width: 576px) {
  .carousel-item img {
    height: 200px !important;
  }
  .product {
    max-width: 100%;
  }
}
/* 产品滑块容器 */
.product-slider-container {
  position: relative;
}
/* 主滑块样式 */
.product-slider {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}
.product-slider .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
}
.product-slider .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  max-height: 500px;
  object-fit: contain;
}
/* 缩略图滑块样式 */
.product-slider-thumbs {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}
.product-slider-thumbs .swiper-slide {
  width: 100%;
  height: 100px;
  opacity: 0.6;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}
.product-slider-thumbs .swiper-slide:hover {
  opacity: 0.8;
}
.product-slider-thumbs .swiper-slide-thumb-active {
  opacity: 1;
  border-color: #000;
}
.product-slider-thumbs .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* 响应式设计 */
@media (max-width: 767.98px) {
  .product-slider-thumbs {
    height: 80px;
    margin-top: 10px;
  }
  
  .product-slider-thumbs .swiper-slide {
    height: 80px;
  }
  
  .product-slider .swiper-slide img {
    max-height: 350px;
  }
}
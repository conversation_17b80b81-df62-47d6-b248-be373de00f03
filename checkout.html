<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="style.css">
</head>
  <body>
    <div class="page-holder">
      <!-- 导航栏-->
      <header class="header bg-white">
        <div class="container px-lg-3">
          <nav class="navbar navbar-expand-lg navbar-light py-3 px-lg-0"><a class="navbar-brand" href="index.html"><span class="fw-bold text-uppercase text-dark">精品店</span></a>
            <button class="navbar-toggler navbar-toggler-end" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="切换导航"><span class="navbar-toggler-icon"></span></button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
              <ul class="navbar-nav me-auto">
                <li class="nav-item">
                  <a class="nav-link" href="index.html">首页</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="shop.html">商店</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="detail.html">产品详情</a>
                </li>
                <li class="nav-item dropdown"><a class="nav-link dropdown-toggle" id="pagesDropdown" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">页面</a>
                  <div class="dropdown-menu mt-3 shadow-sm" aria-labelledby="pagesDropdown"><a class="dropdown-item border-0 transition-link" href="index.html">首页</a><a class="dropdown-item border-0 transition-link" href="shop.html">分类</a><a class="dropdown-item border-0 transition-link" href="detail.html">产品详情</a><a class="dropdown-item border-0 transition-link" href="cart.html">购物车</a><a class="dropdown-item border-0 transition-link" href="checkout.html">结算</a></div>
                </li>
              </ul>
              <ul class="navbar-nav ms-auto">               
                <li class="nav-item"><a class="nav-link" href="cart.html"> <i class="fas fa-dolly-flatbed me-1 text-gray"></i>购物车<small class="text-gray fw-normal">(3)</small></a></li>
                <li class="nav-item"><a class="nav-link" href="sign.html"> <i class="fas fa-user me-1 text-gray fw-normal"></i>登录</a></li>
              </ul>
            </div>
          </nav>
        </div>
      </header>
      <div class="container">
        <!-- 区域-->
        <section class="py-5 bg-light">
          <div class="container">
            <div class="row px-4 px-lg-5 py-lg-4 align-items-center">
              <div class="col-lg-6">
                <h1 class="h2 text-uppercase mb-0">结算</h1>
              </div>
              <div class="col-lg-6 text-lg-end">
                <nav aria-label="面包屑导航">
                  <ol class="breadcrumb justify-content-lg-end mb-0 px-0 bg-light">
                    <li class="breadcrumb-item"><a class="text-dark" href="index.html" style="text-decoration: none;">首页</a></li>
                    <li class="breadcrumb-item"><a class="text-dark" href="cart.html" style="text-decoration: none;">购物车</a></li>
                    <li class="breadcrumb-item active" aria-current="page">结算</li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
        </section>
        <section class="py-5">
          <!-- 账单地址-->
          <h2 class="h5 text-uppercase mb-4">账单详情</h2>
          <div class="row">
            <div class="col-lg-8">
              <form action="#">
                <div class="row gy-3">
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="firstName">名字</label>
                    <input class="form-control form-control-lg" type="text" id="firstName" placeholder="输入您的名字">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="lastName">姓氏</label>
                    <input class="form-control form-control-lg" type="text" id="lastName" placeholder="输入您的姓氏">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="email">电子邮箱</label>
                    <input class="form-control form-control-lg" type="email" id="email" placeholder="例如:<EMAIL>">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="phone">电话号码</label>
                    <input class="form-control form-control-lg" type="tel" id="phone" placeholder="例如：+02 *********">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="company">公司名称（可选）</label>
                    <input class="form-control form-control-lg" type="text" id="company" placeholder="您的公司名称">
                  </div>
                  <div class="col-lg-6 form-group">
                    <label class="form-label text-sm text-uppercase" for="country">城市</label>
                    <select class="country" id="country" data-customclass="form-control form-control-lg rounded-0">
                      <option value>选择您的城市</option>
                      <option value="beijing">北京</option>
                      <option value="shanghai">上海</option>
                      <option value="guangzhou">广州</option>
                      <option value="shenzhen">深圳</option>
                      <option value="chengdu">成都</option>
                      <option value="hangzhou">杭州</option>
                    </select>
                  </div>
                  <div class="col-lg-12">
                    <label class="form-label text-sm text-uppercase" for="address">地址行1</label>
                    <input class="form-control form-control-lg" type="text" id="address" placeholder="门牌号和街道名称">
                  </div>
                  <div class="col-lg-12">
                    <label class="form-label text-sm text-uppercase" for="addressalt">地址行2</label>
                    <input class="form-control form-control-lg" type="text" id="addressalt" placeholder="公寓、套房、单元等（可选）">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="city">城镇/城市</label>
                    <input class="form-control form-control-lg" type="text" id="city">
                  </div>
                  <div class="col-lg-6">
                    <label class="form-label text-sm text-uppercase" for="state">州/县</label>
                    <input class="form-control form-control-lg" type="text" id="state">
                  </div>
                  <div class="col-lg-6">
                    <button class="btn btn-link text-dark p-0 shadow-0" type="button" data-bs-toggle="collapse" data-bs-target="#alternateAddress">
                      <div class="form-check">
                        <input class="form-check-input" id="alternateAddressCheckbox" type="checkbox">
                        <label class="form-check-label" for="alternateAddressCheckbox">备用账单地址</label>
                      </div>
                    </button>
                  </div>
                  <div class="collapse" id="alternateAddress">
                    <div class="row gy-3">
                      <div class="col-12 mt-4">
                        <h2 class="h4 text-uppercase mb-4">备用账单详情</h2>
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="firstName2">名字</label>
                        <input class="form-control form-control-lg" type="text" id="firstName2" placeholder="输入您的名字">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="lastName2">姓氏</label>
                        <input class="form-control form-control-lg" type="text" id="lastName2" placeholder="输入您的姓氏">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="email2">电子邮箱</label>
                        <input class="form-control form-control-lg" type="email" id="email2" placeholder="例如:<EMAIL>">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="phone2">电话号码</label>
                        <input class="form-control form-control-lg" type="tel" id="phone2" placeholder="例如：+02 *********">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="company2">公司名称（可选）</label>
                        <input class="form-control form-control-lg" type="text" id="company2" placeholder="您的公司名称">
                      </div>
                      <div class="col-lg-6 form-group">
                        <label class="form-label text-sm text-uppercase" for="countryAlt">城市</label>
                        <select class="country" id="countryAlt" data-customclass="form-control form-control-lg rounded-0">
                      <option value>选择您的城市</option>
                      <option value="beijing">北京</option>
                      <option value="shanghai">上海</option>
                      <option value="guangzhou">广州</option>
                      <option value="shenzhen">深圳</option>
                      <option value="chengdu">成都</option>
                      <option value="hangzhou">杭州</option>
                        </select>
                      </div>
                      <div class="col-lg-12">
                        <label class="form-label text-sm text-uppercase" for="address2">地址行1</label>
                        <input class="form-control form-control-lg" type="text" id="address2" placeholder="门牌号和街道名称">
                      </div>
                      <div class="col-lg-12">
                        <label class="form-label text-sm text-uppercase" for="addressalt2">地址行2</label>
                        <input class="form-control form-control-lg" type="text" id="addressalt2" placeholder="公寓、套房、单元等（可选）">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="city2">城镇/城市</label>
                        <input class="form-control form-control-lg" type="text" id="city2">
                      </div>
                      <div class="col-lg-6">
                        <label class="form-label text-sm text-uppercase" for="state2">州/县</label>
                        <input class="form-control form-control-lg" type="text" id="state2">
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-12 form-group">
                    <button class="btn btn-dark" type="submit">提交订单</button>
                  </div>
                </div>
              </form>
            </div>
            <!-- 订单摘要-->
            <div class="col-lg-4">
              <div class="card border-0 rounded-0 p-lg-4 bg-light">
                <div class="card-body">
                  <h5 class="text-uppercase mb-4">您的订单</h5>
                  <ul class="list-unstyled mb-0">
                    <li class="d-flex align-items-center justify-content-between"><strong class="small fw-bold">红色数字智能手表</strong><span class="text-muted small">¥250</span></li>
                    <li class="border-bottom my-2"></li>
                    <li class="d-flex align-items-center justify-content-between"><strong class="small fw-bold">灰色耐克跑鞋</strong><span class="text-muted small">¥351</span></li>
                    <li class="border-bottom my-2"></li>
                    <li class="d-flex align-items-center justify-content-between"><strong class="text-uppercase small fw-bold">总计</strong><span>¥601</span></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
<footer class="bg-dark text-white" style="background-color: #000 !important; ">
    <div class="container py-4">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="small text-muted mb-0" style="color: #fff !important;">&copy; 保留所有权利归:</p>
            </div>
        </div>
    </div>
</footer>
      <!-- JavaScript文件-->
      <script src="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/js/bootstrap.bundle.min.js"></script>
      <script src="vendor/glightbox/js/glightbox.min.js"></script>
      <script src="vendor/nouislider/nouislider.min.js"></script>
      <script src="vendor/swiper/swiper-bundle.min.js"></script>
      <script src="vendor/choices.js/public/assets/scripts/choices.min.js"></script>
      <script src="js/front.js"></script>
      <script>
        function injectSvgSprite(path) {
            var ajax = new XMLHttpRequest();
            ajax.open("GET", path, true);
            ajax.send();
            ajax.onload = function(e) {
            var div = document.createElement("div");
            div.className = 'd-none';
            div.innerHTML = ajax.responseText;
            document.body.insertBefore(div, document.body.childNodes[0]);
            }
        }
        injectSvgSprite('icons/orion-svg-sprite.svg'); 
      </script>
    </div>
  </body>
</html>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Document</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/css/bootstrap.min.css">
    <style>
      .register-container {
        max-width: 400px;
        margin: 6.0rem auto;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        background-color: white;
      }
      .register-header {
        text-align: center;
        margin-bottom: 2rem;
      }
      .register-header h2 {
        color: #333;
      }
      .form-control.is-invalid {
        border-color: #dc3545;
      }
      .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
      }
      .login-link {
        text-align: center;
        margin-top: 1.5rem;
      }
    </style>
  </head>
  <body>
    <div class="page-holder">
      <!-- 导航栏-->
      <header class="header bg-white">
        <div class="container px-lg-3">
          <nav class="navbar navbar-expand-lg navbar-light py-3 px-lg-0"><a class="navbar-brand" href="index.html"><span class="fw-bold text-uppercase text-dark">精品店</span></a>
            <button class="navbar-toggler navbar-toggler-end" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="切换导航"><span class="navbar-toggler-icon"></span></button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
              <ul class="navbar-nav me-auto">
                <li class="nav-item">
                  <a class="nav-link" href="index.html">首页</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="shop.html">商店</a>
                </li>
                <li class="nav-item">
                 <a class="nav-link" href="detail.html">产品详情</a>
                </li>
                <li class="nav-item dropdown"><a class="nav-link dropdown-toggle" id="pagesDropdown" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">页面</a>
                  <div class="dropdown-menu mt-3 shadow-sm" aria-labelledby="pagesDropdown"><a class="dropdown-item border-0 transition-link" href="index.html">首页</a><a class="dropdown-item border-0 transition-link" href="shop.html">分类</a><a class="dropdown-item border-0 transition-link" href="detail.html">产品详情</a><a class="dropdown-item border-0 transition-link" href="cart.html">购物车</a><a class="dropdown-item border-0 transition-link" href="checkout.html">结算</a></div>
                </li>
              </ul>
              <ul class="navbar-nav ms-auto">               
                <li class="nav-item"><a class="nav-link" href="cart.html"> <i class="fas fa-dolly-flatbed me-1 text-gray"></i>购物车<small class="text-gray fw-normal">(2)</small></a></li>
                <li class="nav-item"><a class="nav-link" href="sign.html"> <i class="fas fa-user me-1 text-gray fw-normal"></i>登录</a></li>
              </ul>
            </div>
          </nav>
        </div>
      </header>
      <div class="container">
        <div class="register-container">
          <div class="register-header">
            <h2>创建账户</h2>
            <p>加入我们，享受更多专属服务</p>
          </div>
          <form id="registerForm" novalidate>
            <div class="mb-3">
              <label class="form-label" for="username">用户名</label>
              <input type="text" class="form-control" id="username" required minlength="3" maxlength="20">
              <div class="invalid-feedback">
                用户名必须在3-20个字符之间
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="email">邮箱地址</label>
              <input type="email" class="form-control" id="email" required>
              <div class="invalid-feedback">
                请输入有效的邮箱地址
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="password">密码</label>
              <input type="password" class="form-control" id="password" required minlength="8">
              <div class="invalid-feedback">
                密码必须至少8个字符
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="confirmPassword">确认密码</label>
              <input type="password" class="form-control" id="confirmPassword" required>
              <div class="invalid-feedback">
                密码不匹配
              </div>
            </div>
            <button type="submit" class="btn btn-primary w-100">注册</button>
            <div class="login-link mt-3">
              <p>已有账号？<a href="sign.html">立即登录</a></p>
            </div>
          </form>
        </div>
      </div>
<footer class="bg-dark text-white" style="background-color: #000 !important; ">
    <div class="container py-4">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="small text-muted mb-0" style="color: #fff !important;">&copy; 保留所有权利归:</p>
            </div>
        </div>
    </div>
</footer>
      <script src="./bootstrap-5.3.5-dist/bootstrap-5.3.5-dist/js/bootstrap.bundle.min.js"></script>
      <script>
        // 表单验证
        (function () {
          'use strict'         
          const form = document.getElementById('registerForm');
        form.addEventListener('submit', function (event) {
          event.preventDefault(); // 始终阻止默认提交
          event.stopPropagation();

          let isValid = true;
          const username = document.getElementById('username');
          const email = document.getElementById('email');
          const password = document.getElementById('password');
          const confirmPassword = document.getElementById('confirmPassword');

          // 重置验证状态
          form.classList.remove('was-validated');

          // 验证用户名
          if (!username.checkValidity()) {
            username.classList.add('is-invalid');
            isValid = false;
          }

          // 验证邮箱
          if (!email.checkValidity()) {
            email.classList.add('is-invalid');
            isValid = false;
          }

          // 验证密码
          if (!password.checkValidity()) {
            password.classList.add('is-invalid');
            isValid = false;
          }

          // 验证确认密码
          if (password.value !== confirmPassword.value) {
            confirmPassword.classList.add('is-invalid');
            isValid = false;
          }

          if (!isValid) {
            form.classList.add('was-validated');
            return; // 验证失败时直接返回
          }

          // 验证成功时跳转
          window.location.href = 'sign.html';
        });
                  // 实时验证确认密码
                  confirmPassword.addEventListener('input', function() {
                    const password = document.getElementById('password');
                    if (password.value !== confirmPassword.value) {
                      confirmPassword.classList.add('is-invalid');
                    } else {
                      confirmPassword.classList.remove('is-invalid');
                    }
                  });
                })();
      </script>
    </div>
  </body>
</html>